import React, { useState, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { RadioButton } from 'primereact/radiobutton';
import { AutoComplete } from 'primereact/autocomplete';
import { Divider } from 'primereact/divider';
import Button from '@/components/ui/Button/Button';
import CriteriaBuilder from './CriteriaBuilder';
import WorkflowLevels from './WorkflowLevels';
import WorkflowPreview from './WorkflowPreview';
import { ApprovalWorkflow, WorkflowCriteria, CreateApprovalWorkflowRequest, WorkflowActionType, ApprovalAction } from '@/types/approvalWorkflow.types';
import { useFormTypes } from '@/hooks/useApprovalWorkflow';
import { useDepartments } from '@/hooks/useDepartment';
import { UserService } from '@/services/api/userService';
import { UserResponse } from '@/types/api/user';
import './ApprovalWorkflowForm.css';

interface ApprovalWorkflowFormProps {
  workflow?: ApprovalWorkflow | null;
  onSubmit: (data: CreateApprovalWorkflowRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

const ApprovalWorkflowForm: React.FC<ApprovalWorkflowFormProps> = ({
  workflow,
  onSubmit,
  onCancel,
  loading = false
}) => {
  // API hooks
  const { data: formTypes = [], isLoading: formTypesLoading } = useFormTypes();
  const { data: departments = [], isLoading: departmentsLoading } = useDepartments(40928446087168);

  const [formData, setFormData] = useState({
    name: '',
    formType: '',
    criteria: [] as WorkflowCriteria[],
    approvalLevels: 1,
    autoApprove: false,
    autoReject: false,
    actionType: 'approval' as 'approval' | 'auto-approve' | 'auto-reject',
    workflowActionType: 'workflow-levels' as WorkflowActionType,
    selectedDepartmentId: '',
    selectedUserId: null as number | null
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // User search state
  const [userSuggestions, setUserSuggestions] = useState<UserResponse[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserResponse | null>(null);

  // Workflow action type options
  const workflowActionOptions = [
    { label: 'Workflow Levels', value: 'workflow-levels', description: 'Route through multi-level approval hierarchy' },
    { label: 'Department Head', value: 'department-head', description: 'Route to head of selected department' },
    { label: 'Department Head of Record Owner', value: 'department-head-record-owner', description: 'Route to head of requester\'s department' },
    { label: 'Specific User', value: 'specific-user', description: 'Route to a specific selected user' }
  ];

  // Department dropdown options
  const departmentOptions = departments.map(dept => ({
    label: dept.name,
    value: dept.id?.toString() || ''
  }));

  // Initialize form data when workflow prop changes
  useEffect(() => {
    if (workflow) {
      // Determine actionType based on existing workflow data
      let actionType: 'approval' | 'auto-approve' | 'auto-reject' = 'approval';
      if (workflow.autoApprove) {
        actionType = 'auto-approve';
      } else if (workflow.autoReject) {
        actionType = 'auto-reject';
      }

      // Determine workflowActionType based on existing data
      let workflowActionType: WorkflowActionType = 'workflow-levels';
      let selectedDepartmentId = '';
      let selectedUserId: number | null = null;

      // If it's an approval workflow (not auto-approve/auto-reject), determine the action type
      if (actionType === 'approval') {
        if (workflow.workflowActionType) {
          workflowActionType = workflow.workflowActionType;
          selectedDepartmentId = workflow.selectedDepartmentId || '';
          selectedUserId = workflow.selectedUserId || null;
        } else if (workflow.approvals && workflow.approvals.length > 0) {
          // Determine action type from approvals array
          const firstApproval = workflow.approvals[0];
          switch (firstApproval.approverType) {
            case 'DEPARTMENT_HEAD':
              workflowActionType = 'department-head';
              selectedDepartmentId = firstApproval.approver.toString();
              break;
            case 'DEPARTMENT_HEAD_RECORD_OWNER':
              workflowActionType = 'department-head-record-owner';
              break;
            case 'USER':
              workflowActionType = 'specific-user';
              selectedUserId = firstApproval.approver;
              break;
            case 'REPORTING_TO':
            default:
              workflowActionType = 'workflow-levels';
              break;
          }
        }
      }

      setFormData({
        name: workflow.name,
        formType: workflow.formType,
        criteria: workflow.criteria,
        approvalLevels: workflow.approvalLevels,
        autoApprove: workflow.autoApprove,
        autoReject: workflow.autoReject,
        actionType,
        workflowActionType,
        selectedDepartmentId,
        selectedUserId
      });
    } else {
      setFormData({
        name: '',
        formType: '',
        criteria: [],
        approvalLevels: 1,
        autoApprove: false,
        autoReject: false,
        actionType: 'approval',
        workflowActionType: 'workflow-levels',
        selectedDepartmentId: '',
        selectedUserId: null
      });
    }
    setErrors({});
  }, [workflow]);

  // Load selected user when workflow changes and has a selectedUserId
  useEffect(() => {
    const loadSelectedUser = async () => {
      if (workflow?.selectedUserId && formData.workflowActionType === 'specific-user') {
        try {
          // Search for the user by ID to get the full user object
          const users = await UserService.searchUsers(40928446087168, '', undefined, undefined);
          const user = users.find(u => u.id === workflow.selectedUserId);
          if (user) {
            setSelectedUser(user);
          }
        } catch (error) {
          console.error('Error loading selected user:', error);
        }
      } else {
        setSelectedUser(null);
      }
    };

    loadSelectedUser();
  }, [workflow?.selectedUserId, formData.workflowActionType]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate workflow name
    if (!formData.name.trim()) {
      newErrors.name = 'Workflow name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Workflow name must be at least 3 characters long';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Workflow name must be less than 100 characters';
    }

    // Validate form type
    if (!formData.formType) {
      newErrors.formType = 'Form type is required';
    }

    // Validate criteria
    if (formData.criteria.length === 0) {
      newErrors.criteria = 'At least one criteria is required';
    } else {
      // Validate each criteria
      const invalidCriteria = formData.criteria.find(criterion =>
        !criterion.fieldType || !criterion.operator || !criterion.value
      );

      if (invalidCriteria) {
        newErrors.criteria = 'All criteria must have field type, operator, and value selected';
      }

      // Validate logic connectors for multiple criteria
      if (formData.criteria.length > 1) {
        const invalidConnector = formData.criteria.slice(0, -1).find(criterion =>
          !criterion.logicConnector
        );

        if (invalidConnector) {
          newErrors.criteria = 'Logic connectors (AND/OR) are required between criteria';
        }
      }
    }

    // Validate action type conflicts
    if (formData.autoApprove && formData.autoReject) {
      newErrors.autoActions = 'Cannot enable both auto-approve and auto-reject';
    }

    // Validate approval levels for approval workflows
    if (formData.actionType === 'approval' && formData.approvalLevels < 1) {
      newErrors.approvalLevels = 'At least one approval level is required';
    }

    // Validate department selection for department-head workflow action
    if (formData.actionType === 'approval' && formData.workflowActionType === 'department-head') {
      if (!formData.selectedDepartmentId) {
        newErrors.selectedDepartmentId = 'Please select a department';
      } else {
        // Check if selected department has a department head
        const selectedDept = departments.find(dept => dept.id?.toString() === formData.selectedDepartmentId);
        if (!selectedDept?.departmentHeadId) {
          newErrors.selectedDepartmentId = 'Selected department does not have a department head assigned';
        }
      }
    }

    // Validate user selection for specific-user workflow action
    if (formData.actionType === 'approval' && formData.workflowActionType === 'specific-user' && !formData.selectedUserId) {
      newErrors.selectedUserId = 'Please select a user';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Check if form is valid for real-time validation
  const isFormValid = (): boolean => {
    return Boolean(
      formData.name.trim().length >= 3 &&
      formData.formType &&
      formData.criteria.length > 0 &&
      formData.criteria.every(criterion =>
        criterion.fieldType && criterion.operator && criterion.value
      ) &&
      (formData.criteria.length === 1 ||
       formData.criteria.slice(0, -1).every(criterion => criterion.logicConnector)) &&
      !(formData.autoApprove && formData.autoReject) &&
      (formData.actionType !== 'approval' || formData.approvalLevels >= 1)
    );
  };

  // Transform form data to include approvals array
  const transformFormDataForSubmission = (): CreateApprovalWorkflowRequest => {
    let approvals: ApprovalAction[] = [];

    if (formData.actionType === 'approval') {
      switch (formData.workflowActionType) {
        case 'workflow-levels':
          approvals = [{ approver: formData.approvalLevels, approverType: 'REPORTING_TO' }];
          break;
        case 'department-head':
          // For department head, we need to get the department head's user ID from the selected department
          const selectedDept = departments.find(dept => dept.id?.toString() === formData.selectedDepartmentId);
          const departmentHeadId = selectedDept?.departmentHeadId;
          if (departmentHeadId) {
            approvals = [{ approver: departmentHeadId, approverType: 'DEPARTMENT_HEAD' }];
          } else {
            // Fallback: use department ID if no head is assigned
            const departmentId = parseInt(formData.selectedDepartmentId);
            approvals = [{ approver: departmentId, approverType: 'DEPARTMENT_HEAD' }];
          }
          break;
        case 'department-head-record-owner':
          approvals = [{ approver: 1, approverType: 'DEPARTMENT_HEAD_RECORD_OWNER' }];
          break;
        case 'specific-user':
          if (formData.selectedUserId) {
            approvals = [{ approver: formData.selectedUserId, approverType: 'USER' }];
          }
          break;
      }
    }

    return {
      name: formData.name,
      formType: formData.formType,
      criteria: formData.criteria,
      approvalLevels: formData.approvalLevels,
      autoApprove: formData.autoApprove,
      autoReject: formData.autoReject,
      workflowActionType: formData.workflowActionType,
      selectedDepartmentId: formData.selectedDepartmentId || undefined,
      selectedUserId: formData.selectedUserId || undefined,
      approvals
    };
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      const submissionData = transformFormDataForSubmission();
      onSubmit(submissionData);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle criteria changes
  const handleCriteriaChange = (criteria: WorkflowCriteria[]) => {
    handleInputChange('criteria', criteria);

    // Clear criteria error if criteria are now valid
    if (criteria.length > 0 &&
        criteria.every(criterion => criterion.fieldType && criterion.operator && criterion.value) &&
        (criteria.length === 1 || criteria.slice(0, -1).every(criterion => criterion.logicConnector))) {
      if (errors.criteria) {
        setErrors(prev => ({
          ...prev,
          criteria: ''
        }));
      }
    }
  };

  // Handle approval levels change
  const handleApprovalLevelsChange = (levels: number) => {
    handleInputChange('approvalLevels', levels);
  };

  // Handle action type change (approval vs auto actions)
  const handleActionTypeChange = (actionType: 'approval' | 'auto-approve' | 'auto-reject') => {
    setFormData(prev => ({
      ...prev,
      actionType,
      autoApprove: actionType === 'auto-approve',
      autoReject: actionType === 'auto-reject',
      // Reset approval levels when switching to auto actions
      approvalLevels: actionType === 'approval' ? prev.approvalLevels : 1,
      // Reset workflow action type when switching away from approval
      workflowActionType: actionType === 'approval' ? prev.workflowActionType : 'workflow-levels',
      selectedDepartmentId: actionType === 'approval' ? prev.selectedDepartmentId : '',
      selectedUserId: actionType === 'approval' ? prev.selectedUserId : null
    }));

    // Clear any related errors
    if (errors.autoActions) {
      setErrors(prev => ({
        ...prev,
        autoActions: ''
      }));
    }
  };

  // Handle workflow action type change
  const handleWorkflowActionTypeChange = (workflowActionType: WorkflowActionType) => {
    setFormData(prev => ({
      ...prev,
      workflowActionType,
      // Reset department selection when switching away from department-head
      selectedDepartmentId: workflowActionType === 'department-head' ? prev.selectedDepartmentId : '',
      // Reset user selection when switching away from specific-user
      selectedUserId: workflowActionType === 'specific-user' ? prev.selectedUserId : null
    }));

    // Reset selected user state when switching away from specific-user
    if (workflowActionType !== 'specific-user') {
      setSelectedUser(null);
    }

    // Clear related errors
    if (errors.selectedDepartmentId) {
      setErrors(prev => ({
        ...prev,
        selectedDepartmentId: ''
      }));
    }
    if (errors.selectedUserId) {
      setErrors(prev => ({
        ...prev,
        selectedUserId: ''
      }));
    }
  };

  // Handle department selection change
  const handleDepartmentChange = (departmentId: string) => {
    handleInputChange('selectedDepartmentId', departmentId);

    // Clear related errors
    if (errors.selectedDepartmentId) {
      setErrors(prev => ({
        ...prev,
        selectedDepartmentId: ''
      }));
    }
  };

  // User search functionality
  const searchUsers = async (query: string) => {
    if (!query || query.length < 2) {
      setUserSuggestions([]);
      return;
    }

    try {
      const users = await UserService.searchUsers(40928446087168, query, undefined, undefined);
      setUserSuggestions(users);
    } catch (error) {
      console.error('Error searching users:', error);
      setUserSuggestions([]);
    }
  };

  // Handle user search input
  const handleUserSearch = (event: { query: string }) => {
    searchUsers(event.query);
  };

  // Handle user selection change
  const handleUserChange = (user: UserResponse | null) => {
    setSelectedUser(user);
    setFormData(prev => ({
      ...prev,
      selectedUserId: user?.id || null
    }));

    // Clear related errors
    if (errors.selectedUserId) {
      setErrors(prev => ({
        ...prev,
        selectedUserId: ''
      }));
    }
  };

  // Custom item template for user dropdown
  const userItemTemplate = (user: UserResponse) => {
    return (
      <div className="flex align-items-center">
        <i className="pi pi-user mr-2"></i>
        <div>
          <div className="font-medium">{user.name}</div>
          <div className="text-sm text-color-secondary">{user.email}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="approval-workflow-form">
      <form onSubmit={handleSubmit}>
        {/* Basic Information Section */}
        <div className="form-section">
          <h3 className="section-title">Basic Information</h3>

          <div className="form-grid">
            <div className="form-field">
              <label htmlFor="workflow-name" className="field-label">
                Workflow Name <span className="required">*</span>
              </label>
              <InputText
                id="workflow-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter workflow name"
                className={`w-full ${errors.name ? 'p-invalid' : ''}`}
              />
              {errors.name && <small className="p-error">{errors.name}</small>}
            </div>

            <div className="form-field">
              <label htmlFor="form-type" className="field-label">
                Form Type <span className="required">*</span>
              </label>
              <Dropdown
                id="form-type"
                value={formData.formType}
                options={formTypes}
                onChange={(e) => handleInputChange('formType', e.value)}
                placeholder="Select form type"
                className={`w-full ${errors.formType ? 'p-invalid' : ''}`}
                filter
                loading={formTypesLoading}
              />
              {errors.formType && <small className="p-error">{errors.formType}</small>}
            </div>
          </div>
        </div>

        <Divider />

        {/* Criteria Section */}
        <div className="form-section">
          <h3 className="section-title">Criteria Configuration</h3>
          <p className="section-description">
            Define the conditions that will trigger this approval workflow.
          </p>

          <CriteriaBuilder
            criteria={formData.criteria}
            onChange={handleCriteriaChange}
            error={errors.criteria}
          />
        </div>

        <Divider />

        {/* Action Type Selection */}
        <div className="form-section">
          <h3 className="section-title">Workflow Action</h3>
          <p className="section-description">
            Choose how requests matching the criteria should be handled.
          </p>

          <div className="action-type-selection">
            {/* Approval Workflow Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-approval"
                  value="approval"
                  checked={formData.actionType === 'approval'}
                  onChange={(e) => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-approval" className="action-label">
                  Approval Workflow
                </label>
              </div>
              <div className="action-description">
                Route requests through a multi-level approval process
              </div>

              {/* Show workflow action options when approval is selected */}
              {formData.actionType === 'approval' && (
                <div className="workflow-action-container">
                  <div className="form-field mt-3">
                    <label htmlFor="workflow-action-type" className="field-label">
                      Workflow Action Type <span className="required">*</span>
                    </label>
                    <Dropdown
                      id="workflow-action-type"
                      value={formData.workflowActionType}
                      options={workflowActionOptions}
                      onChange={(e) => handleWorkflowActionTypeChange(e.value)}
                      placeholder="Select workflow action type"
                      className="w-full"
                      optionLabel="label"
                      optionValue="value"
                    />
                    <small className="field-description">
                      {workflowActionOptions.find(opt => opt.value === formData.workflowActionType)?.description}
                    </small>
                  </div>

                  {/* Show workflow levels for workflow-levels action type */}
                  {formData.workflowActionType === 'workflow-levels' && (
                    <div className="approval-levels-container mt-3">
                      <WorkflowLevels
                        levels={formData.approvalLevels}
                        onChange={handleApprovalLevelsChange}
                      />
                      {errors.approvalLevels && (
                        <small className="p-error">{errors.approvalLevels}</small>
                      )}
                    </div>
                  )}

                  {/* Show department selection for department-head action type */}
                  {formData.workflowActionType === 'department-head' && (
                    <div className="department-selection-container mt-3">
                      <div className="form-field">
                        <label htmlFor="department-selection" className="field-label">
                          Select Department <span className="required">*</span>
                        </label>
                        <Dropdown
                          id="department-selection"
                          value={formData.selectedDepartmentId}
                          options={departmentOptions}
                          onChange={(e) => handleDepartmentChange(e.value)}
                          placeholder="Select department"
                          className={`w-full ${errors.selectedDepartmentId ? 'p-invalid' : ''}`}
                          optionLabel="label"
                          optionValue="value"
                          filter
                          loading={departmentsLoading}
                        />
                        {errors.selectedDepartmentId && (
                          <small className="p-error">{errors.selectedDepartmentId}</small>
                        )}
                        <small className="field-description">
                          Requests will be routed to the head of the selected department
                        </small>
                      </div>
                    </div>
                  )}

                  {/* Show user selection for specific-user action type */}
                  {formData.workflowActionType === 'specific-user' && (
                    <div className="user-selection-container mt-3">
                      <div className="form-field">
                        <label htmlFor="user-selection" className="field-label">
                          Select User <span className="required">*</span>
                        </label>
                        <AutoComplete
                          id="user-selection"
                          value={selectedUser || undefined}
                          suggestions={userSuggestions}
                          completeMethod={handleUserSearch}
                          onChange={(e) => handleUserChange(e.value)}
                          placeholder="Search for user"
                          className={`w-full ${errors.selectedUserId ? 'p-invalid' : ''}`}
                          itemTemplate={userItemTemplate}
                          field="name"
                          forceSelection={true}
                          emptyMessage="No users found"
                        />
                        {errors.selectedUserId && (
                          <small className="p-error">{errors.selectedUserId}</small>
                        )}
                        <small className="field-description">
                          Requests will be routed to the selected user for approval
                        </small>
                      </div>
                    </div>
                  )}

                  {/* Show description for department-head-record-owner action type */}
                  {formData.workflowActionType === 'department-head-record-owner' && (
                    <div className="record-owner-description mt-3">
                      <div className="info-box">
                        <i className="pi pi-info-circle"></i>
                        <span>
                          Requests will be automatically routed to the department head of the person who initiated the request.
                          No additional configuration is required.
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Auto-approve Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-auto-approve"
                  value="auto-approve"
                  checked={formData.actionType === 'auto-approve'}
                  onChange={(e) => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-auto-approve" className="action-label">
                  Auto-approve
                </label>
              </div>
              <div className="action-description">
                Automatically approve requests matching criteria
              </div>
            </div>

            {/* Auto-reject Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-auto-reject"
                  value="auto-reject"
                  checked={formData.actionType === 'auto-reject'}
                  onChange={(e) => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-auto-reject" className="action-label">
                  Auto-reject
                </label>
              </div>
              <div className="action-description">
                Automatically reject requests matching criteria
              </div>
            </div>
          </div>

          {errors.autoActions && (
            <small className="p-error">{errors.autoActions}</small>
          )}
        </div>

        <Divider />

        {/* Workflow Preview Section */}
        <div className="form-section">
          {/* <h3 className="section-title">Workflow Preview</h3>
          <p className="section-description">
            Preview how this workflow will operate.
          </p> */}

          <WorkflowPreview
            criteria={formData.criteria}
            approvalLevels={formData.approvalLevels}
            autoApprove={formData.autoApprove}
            autoReject={formData.autoReject}
            workflowActionType={formData.workflowActionType}
            selectedDepartmentId={formData.selectedDepartmentId}
            selectedDepartmentName={departmentOptions.find(dept => dept.value === formData.selectedDepartmentId)?.label}
            selectedUserId={formData.selectedUserId || undefined}
            selectedUserName={selectedUser?.name}
          />
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={loading}
            disabled={loading || !isFormValid()}
          >
            {workflow ? 'Update Workflow' : 'Create Workflow'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ApprovalWorkflowForm;
